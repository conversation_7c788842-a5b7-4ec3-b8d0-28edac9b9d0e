export interface GetRecentFiles {
  datas: RecentFileDetails[];
}

export interface RecentFileDetails {
  id: number;
  name: string;
  status: string;
  actionType: string;
  documentType: string;
  thumbnailUrl: string;
  backgroundColor: string;
  latestEditedAt: Date | null;
  department: {
    id: number;
    nameTh: string;
  };
  permission: {
    canDelete: boolean;
  };
}

export interface GetRecentTemplatesByCreator {
  datas: RecentTemplateDetails[];
  pagination: any;
  totalApproved: number;
  totalPending: number;
  totalTemplates: number;
}

export interface RecentTemplateDetails {
  id: number;
  name: string;
  status: string;
  actionType: string;
  thumbnailUrl: string;
  backgroundColor: string;
  department: { id: number; nameTh: string };
  permission: { canDelete: boolean };
}

export interface GetRecentReportsByCreator {
  datas: RecentReportDetails[];
  pagination: any;
  totalApproved: number;
  totalPending: number;
  totalReports: number;
}

export interface RecentReportDetails {
  id: number;
  name: string;
  status: string;
  actionType: string;
  thumbnailUrl: string;
  backgroundColor: string;
  department: { id: number; nameTh: string };
  permission: { canDelete: boolean };
}
