import { Controller, Get, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { Users } from '@core/db/entities/users';
import { ReqUser } from '@core/middleware/jwt/jwt.common';
import { ApiException } from '@core/shared/http/http.exception';

import { TemplatesV1Service } from '../../templates/templates.v1.service';
import { ReportsV1Service } from '../../reports/reports.v1.service';
import { GetRecentTemplatesV1HttpParam, GetRecentTemplatesV1HttpResponse } from '../../templates/handler/dto/get-recent-templates.v1.http.dto';
import { GetRecentReportsV1HttpParam, GetRecentReportsV1HttpResponse } from '../../reports/handler/dto/get-recent-reports.v1.http.dto';
import { RecentFilesV1Service } from '../../recent-files.v1.service';
import {
  GetRecentFilesV1HttpParam,
  GetRecentFilesV1HttpResponse,
} from '../dto/get-recent-files.v1.http.dto';

@ApiTags('RecentFilesControllerV1')
@ApiBearerAuth()
@Controller({
  path: 'recent-files',
  version: '1',
})
export class RecentFilesV1HttpController {
  constructor(private recentFilesService: RecentFilesV1Service) {}

  @Get()
  async getRecentFiles(
    @Query() options: GetRecentFilesV1HttpParam,
    @ReqUser() user: Users,
  ): Promise<GetRecentFilesV1HttpResponse> {
    const r = await this.recentFilesService.getRecentFiles(
      user,
      options.limit || 10,
    );

    return r.match(
      (data) => ({
        success: true,
        key: '',
        data: data.datas,
      }),
      (e) => {
        throw new ApiException(e, 500);
      },
    );
  }
}
