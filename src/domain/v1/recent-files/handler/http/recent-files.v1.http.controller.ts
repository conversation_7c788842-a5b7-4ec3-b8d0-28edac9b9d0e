import { Controller, Get, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { Users } from '@core/db/entities/users';
import { ReqUser } from '@core/middleware/jwt/jwt.common';
import { ApiException } from '@core/shared/http/http.exception';

import { RecentFilesV1Service } from '../../recent-files.v1.service';
import {
  GetRecentFilesV1HttpParam,
  GetRecentFilesV1HttpResponse,
} from '../dto/get-recent-files.v1.http.dto';
import {
  GetRecentReportsV1HttpParam,
  GetRecentReportsV1HttpResponse,
} from '../dto/get-recent-reports.v1.http.dto';
import {
  GetRecentTemplatesV1HttpParam,
  GetRecentTemplatesV1HttpResponse,
} from '../dto/get-recent-templates.v1.http.dto';

@ApiTags('RecentFilesControllerV1')
@ApiBearerAuth()
@Controller({
  path: 'recent-files',
  version: '1',
})
export class RecentFilesV1HttpController {
  constructor(private recentFilesService: RecentFilesV1Service) {}

  @Get()
  async getRecentFiles(
    @Query() options: GetRecentFilesV1HttpParam,
    @ReqUser() user: Users,
  ): Promise<GetRecentFilesV1HttpResponse> {
    const r = await this.recentFilesService.getRecentFiles(
      user,
      options.limit || 10,
    );

    return r.match(
      (data) => ({
        success: true,
        key: '',
        data: data.datas,
      }),
      (e) => {
        throw new ApiException(e, 500);
      },
    );
  }

  @Get('templates')
  async getRecentTemplates(
    @Query() options: GetRecentTemplatesV1HttpParam,
    @ReqUser() user: Users,
  ): Promise<GetRecentTemplatesV1HttpResponse> {
    const r = await this.recentFilesService.getRecentTemplatesByCreator(
      user,
      options,
    );

    return r.match(
      (data) => ({
        success: true,
        key: '',
        data: data.datas,
        meta: {
          pagination: data.pagination,
          totalApproved: data.totalApproved,
          totalPending: data.totalPending,
          totalTemplates: data.totalTemplates,
        },
      }),
      (e) => {
        throw new ApiException(e, 500);
      },
    );
  }

  @Get('reports')
  async getRecentReports(
    @Query() options: GetRecentReportsV1HttpParam,
    @ReqUser() user: Users,
  ): Promise<GetRecentReportsV1HttpResponse> {
    const r = await this.recentFilesService.getRecentReportsByCreator(
      user,
      options,
    );

    return r.match(
      (data) => ({
        success: true,
        key: '',
        data: data.datas,
        meta: {
          pagination: data.pagination,
          totalApproved: data.totalApproved,
          totalPending: data.totalPending,
          totalReports: data.totalReports,
        },
      }),
      (e) => {
        throw new ApiException(e, 500);
      },
    );
  }
}
