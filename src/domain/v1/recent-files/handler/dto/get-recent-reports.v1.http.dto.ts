import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';

import {
  transformToNumberArray,
  transformToStringArray,
} from '@core/shared/common/common.func';
import { PaginationOptions } from '@core/shared/common/common.pagintaion';
import {
  TypeOrmOrderBy,
  TypeOrmOrderByType,
} from '@core/shared/common/common.typeorm';
import { PaginationResponseSchema } from '@core/shared/http/http.response.dto';
import { IStandardArrayApiResponse } from '@core/shared/http/http.standard';

import { ApproveStatus } from '@domain/v1/documents/documents.v1.constant';
import { LayoutDirection } from '@domain/v1/layouts/laytous.v1.constant';

import { ReportPaginateDetails } from '../../../reports/reports.v1.type';

export class GetRecentReportsV1HttpParam implements PaginationOptions {
  @ApiProperty({
    example: 1,
    type: Number,
  })
  @IsNumber()
  @Type(() => Number)
  page: number;

  @ApiProperty({
    example: 10,
    type: Number,
  })
  @IsNumber()
  @Type(() => Number)
  perPage: number;

  @ApiPropertyOptional({
    type: String,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    type: Date,
    example: '2025-06-16',
    description: 'Filter by start date (YYYY-MM-DD)',
  })
  @IsOptional()
  @Type(() => Date)
  startDate?: Date;

  @ApiPropertyOptional({
    type: Date,
    example: '2025-06-16',
    description: 'Filter by end date (YYYY-MM-DD)',
  })
  @IsOptional()
  @Type(() => Date)
  endDate?: Date;

  @ApiPropertyOptional({
    type: String,
    example: '[1,2,3]',
  })
  @IsOptional()
  @Transform(({ value }) => transformToNumberArray(value))
  departmentIds?: number[];

  @ApiPropertyOptional({
    type: String,
    example: '[1,2,3]',
  })
  @IsOptional()
  @Transform(({ value }) => transformToNumberArray(value))
  disasterIds?: number[];

  @ApiPropertyOptional({
    type: String,
    example: `["${LayoutDirection.horizontal}","${LayoutDirection.vertical}"]`,
  })
  @IsOptional()
  @Transform(({ value }) => transformToStringArray(value))
  layoutDirections?: string[];

  @ApiPropertyOptional({
    enum: ['name', 'reviewedAt', 'latestEditedAt', 'createdAt'],
    default: 'latestEditedAt',
    example: 'latestEditedAt',
  })
  @IsOptional()
  @IsEnum(['name', 'reviewedAt', 'latestEditedAt', 'createdAt'])
  sortBy?: string;

  @ApiPropertyOptional({
    enum: TypeOrmOrderBy,
    default: TypeOrmOrderBy.DESC,
    example: TypeOrmOrderBy.DESC,
  })
  @IsOptional()
  @IsEnum(TypeOrmOrderBy)
  orderBy?: TypeOrmOrderByType;

  @ApiPropertyOptional({
    enum: [
      ApproveStatus.Draft,
      ApproveStatus.WaitForApprove,
      ApproveStatus.Approved,
      ApproveStatus.Rejected,
    ],
    example: ApproveStatus.Approved,
    description: 'Filter by document status',
  })
  @IsOptional()
  @IsEnum([
    ApproveStatus.Draft,
    ApproveStatus.WaitForApprove,
    ApproveStatus.Approved,
    ApproveStatus.Rejected,
  ])
  status?: string;
}

class GetRecentReportsV1HttpData implements ReportPaginateDetails {
  id: number;
  name: string;
  status: string;
  actionType: string;
  thumbnailUrl: string;
  backgroundColor: string;
  department: { id: number; nameTh: string };
  permission: { canDelete: boolean };
}

export class RecentReportsMetaResponse {
  pagination: PaginationResponseSchema;
  totalApproved: number;
  totalPending: number;
  totalDraft: number;
  totalReports: number;
}

export class GetRecentReportsV1HttpResponse
  implements IStandardArrayApiResponse
{
  success: boolean;
  key: string;
  data: GetRecentReportsV1HttpData[];
  meta: RecentReportsMetaResponse;
}
