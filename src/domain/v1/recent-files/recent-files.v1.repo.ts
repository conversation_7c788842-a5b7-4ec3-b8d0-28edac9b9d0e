import { Injectable } from '@nestjs/common';

import { Documents } from '@core/db/entities/documents';
import { BaseRepo } from '@core/shared/common/common.repo';
import tzDayjs from '@core/shared/common/common.dayjs';
import {
  PaginationOptions,
  getLimit,
  getOffset,
  getPagination,
} from '@core/shared/common/common.pagintaion';
import { TypeOrmOrderByType } from '@core/shared/common/common.typeorm';

import {
  ApproveStatus,
  DocumentType,
} from '../documents/documents.v1.constant';

@Injectable()
export class RecentFilesV1Repo extends BaseRepo {
  async getRecentFiles(limit: number = 10) {
    const qb = this.from(Documents)
      .createQueryBuilder('doc')
      .select([
        'doc.id',
        'doc.name',
        'doc.status',
        'doc.actionType',
        'doc.documentType',
        'doc.backgroundColor',
        'doc.latestEditedAt',
        'thumbnail.resolutionLowPath',
        'createdBy.id',
        'department.id',
        'department.nameTh',
      ])
      .leftJoin('doc.thumbnail', 'thumbnail')
      .leftJoin('doc.createdBy', 'createdBy')
      .leftJoin('createdBy.department', 'department')
      .where('doc.documentType IN (:...documentTypes)', {
        documentTypes: [DocumentType.Template, DocumentType.Report],
      })
      .andWhere('doc.status = :status', {
        status: ApproveStatus.Approved,
      })
      .andWhere('doc.latestEditedAt IS NOT NULL')
      .andWhere('doc.deletedAt IS NULL')
      .orderBy('doc.latestEditedAt', 'DESC')
      .limit(limit);

    const datas = await qb.getMany();

    return {
      datas: datas.map((data) => ({
        id: data.id,
        name: data.name,
        status: data.status,
        actionType: data.actionType,
        documentType: data.documentType,
        backgroundColor: data.backgroundColor,
        latestEditedAt: data.latestEditedAt,
        thumbnail: data.thumbnail,
        createdBy: {
          id: data.createdBy.id,
        },
        department: {
          id: data.createdBy?.department?.id,
          nameTh: data.createdBy?.department?.nameTh,
        },
      })),
    };
  }

  async getRecentTemplatesByCreator(
    createdById: number,
    paginatedOptions: PaginationOptions,
    query: {
      search?: string;
      startDate?: Date;
      endDate?: Date;
      departmentIds?: number[];
      disasterIds?: number[];
      layoutDirections?: string[];
      status?: string;
      orderBy: TypeOrmOrderByType;
      sortBy: string;
    },
  ) {
    const qb = this.from(Documents)
      .createQueryBuilder('doc')
      .select([
        'doc.id',
        'doc.name',
        'doc.status',
        'doc.actionType',
        'doc.backgroundColor',
        'doc.reviewedAt',
        'doc.createdAt',
        'doc.latestEditedAt',
        'layout.id',
        'disaster.id',
        'thumbnail.resolutionLowPath',
        'createdBy.id',
        'department.id',
        'department.nameTh',
      ])
      .leftJoin('doc.layout', 'layout')
      .leftJoin('doc.disasters', 'disaster')
      .leftJoin('doc.thumbnail', 'thumbnail')
      .leftJoin('doc.createdBy', 'createdBy')
      .leftJoin('createdBy.department', 'department')
      .where('doc.documentType = :documentType', {
        documentType: DocumentType.Template,
      })
      .andWhere('doc.createdById = :createdById', {
        createdById,
      })
      .andWhere('doc.deletedAt IS NULL');

    if (query.status) {
      qb.andWhere('doc.status = :status', {
        status: query.status,
      });
    }

    if (query.search) {
      qb.andWhere('doc.name ILIKE :search', {
        search: `%${query.search.trim()}%`,
      });
    }

    if (query.startDate) {
      qb.andWhere('doc.reviewedAt >= :startDate', {
        startDate: tzDayjs(query.startDate).startOf('day').toDate(),
      });
    }

    if (query.endDate) {
      qb.andWhere('doc.reviewedAt <= :endDate', {
        endDate: tzDayjs(query.endDate).endOf('day').toDate(),
      });
    }

    if (query.departmentIds && query.departmentIds.length > 0) {
      qb.andWhere('department.id IN (:...departmentIds)', {
        departmentIds: query.departmentIds,
      });
    }

    if (query.layoutDirections && query.layoutDirections.length > 0) {
      qb.andWhere('layout.direction IN (:...layoutDirections)', {
        layoutDirections: query.layoutDirections,
      });
    }

    if (query.disasterIds && query.disasterIds.length > 0) {
      qb.andWhere('disaster.id IN (:...disasterIds)', {
        disasterIds: query.disasterIds,
      });
    }

    if (query.sortBy === 'name') {
      qb.orderBy('doc.name', query.orderBy);
    } else if (query.sortBy === 'latestEditedAt') {
      qb.orderBy('doc.latestEditedAt', query.orderBy);
    } else if (query.sortBy === 'createdAt') {
      qb.orderBy('doc.createdAt', query.orderBy);
    } else {
      qb.orderBy('doc.reviewedAt', query.orderBy);
    }

    const datas = await qb
      .take(getLimit(paginatedOptions))
      .skip(getOffset(paginatedOptions))
      .getMany();

    const totalItems = await qb.getCount();

    return {
      datas: datas.map((data) => ({
        id: data.id,
        name: data.name,
        status: data.status,
        actionType: data.actionType,
        backgroundColor: data.backgroundColor,
        thumbnail: data.thumbnail,
        createdBy: {
          id: data.createdBy.id,
        },
        department: {
          id: data.createdBy?.department?.id,
          nameTh: data.createdBy?.department?.nameTh,
        },
      })),
      pagination: getPagination(datas, totalItems, paginatedOptions),
    };
  }

  async getRecentReportsByCreator(
    createdById: number,
    paginatedOptions: PaginationOptions,
    query: {
      search?: string;
      startDate?: Date;
      endDate?: Date;
      departmentIds?: number[];
      layoutDirections?: string[];
      disasterIds?: number[];
      status?: string;
      orderBy: TypeOrmOrderByType;
      sortBy: string;
    },
  ) {
    const qb = this.from(Documents)
      .createQueryBuilder('doc')
      .select([
        'doc.id',
        'doc.name',
        'doc.status',
        'doc.actionType',
        'doc.backgroundColor',
        'doc.reviewedAt',
        'doc.createdAt',
        'doc.latestEditedAt',
        'doc.responsibilityAreaId',
        'layout.id',
        'disaster.id',
        'thumbnail.resolutionLowPath',
        'createdBy.id',
        'department.id',
        'department.nameTh',
      ])
      .leftJoin('doc.layout', 'layout')
      .leftJoin('doc.disasters', 'disaster')
      .leftJoin('doc.thumbnail', 'thumbnail')
      .leftJoin('doc.createdBy', 'createdBy')
      .leftJoin('createdBy.department', 'department')
      .where('doc.documentType = :documentType', {
        documentType: DocumentType.Report,
      })
      .andWhere('doc.createdById = :createdById', {
        createdById,
      })
      .andWhere('doc.deletedAt IS NULL');

    if (query.status) {
      qb.andWhere('doc.status = :status', {
        status: query.status,
      });
    }

    if (query.search) {
      qb.andWhere('doc.name ILIKE :search', {
        search: `%${query.search.trim()}%`,
      });
    }

    if (query.startDate) {
      qb.andWhere('doc.reviewedAt >= :startDate', {
        startDate: tzDayjs(query.startDate).startOf('day').toDate(),
      });
    }

    if (query.endDate) {
      qb.andWhere('doc.reviewedAt <= :endDate', {
        endDate: tzDayjs(query.endDate).endOf('day').toDate(),
      });
    }

    if (query.departmentIds && query.departmentIds.length > 0) {
      qb.andWhere('department.id IN (:...departmentIds)', {
        departmentIds: query.departmentIds,
      });
    }

    if (query.layoutDirections && query.layoutDirections.length > 0) {
      qb.andWhere('layout.direction IN (:...layoutDirections)', {
        layoutDirections: query.layoutDirections,
      });
    }

    if (query.disasterIds && query.disasterIds.length > 0) {
      qb.andWhere('disaster.id IN (:...disasterIds)', {
        disasterIds: query.disasterIds,
      });
    }

    if (query.sortBy === 'name') {
      qb.orderBy('doc.name', query.orderBy);
    } else if (query.sortBy === 'latestEditedAt') {
      qb.orderBy('doc.latestEditedAt', query.orderBy);
    } else if (query.sortBy === 'createdAt') {
      qb.orderBy('doc.createdAt', query.orderBy);
    } else {
      qb.orderBy('doc.reviewedAt', query.orderBy);
    }

    const datas = await qb
      .take(getLimit(paginatedOptions))
      .skip(getOffset(paginatedOptions))
      .getMany();

    const totalItems = await qb.getCount();

    return {
      datas: datas.map((data) => ({
        id: data.id,
        name: data.name,
        status: data.status,
        actionType: data.actionType,
        backgroundColor: data.backgroundColor,
        thumbnail: data.thumbnail,
        createdBy: {
          id: data.createdBy.id,
        },
        department: {
          id: data.createdBy?.department?.id,
          nameTh: data.createdBy?.department?.nameTh,
        },
      })),
      pagination: getPagination(datas, totalItems, paginatedOptions),
    };
  }

  async getApprovedTemplateCountsByCreator(createdById: number): Promise<number> {
    return await this.from(Documents).countBy({
      documentType: DocumentType.Template,
      status: ApproveStatus.Approved,
      createdById,
    });
  }

  async getPendingTemplateCountsByCreator(createdById: number): Promise<number> {
    return await this.from(Documents).countBy({
      documentType: DocumentType.Template,
      status: ApproveStatus.WaitForApprove,
      createdById,
    });
  }

  async getDraftTemplateCountsByCreator(createdById: number): Promise<number> {
    return await this.from(Documents).countBy({
      documentType: DocumentType.Template,
      status: ApproveStatus.Draft,
      createdById,
    });
  }

  async getTotalTemplateCountsByCreator(createdById: number): Promise<number> {
    return await this.from(Documents).countBy({
      documentType: DocumentType.Template,
      createdById,
    });
  }

  async getApprovedReportCountsByCreator(createdById: number): Promise<number> {
    return await this.from(Documents).countBy({
      documentType: DocumentType.Report,
      status: ApproveStatus.Approved,
      createdById,
    });
  }

  async getPendingReportCountsByCreator(createdById: number): Promise<number> {
    return await this.from(Documents).countBy({
      documentType: DocumentType.Report,
      status: ApproveStatus.WaitForApprove,
      createdById,
    });
  }

  async getDraftReportCountsByCreator(createdById: number): Promise<number> {
    return await this.from(Documents).countBy({
      documentType: DocumentType.Report,
      status: ApproveStatus.Draft,
      createdById,
    });
  }

  async getTotalReportCountsByCreator(createdById: number): Promise<number> {
    return await this.from(Documents).countBy({
      documentType: DocumentType.Report,
      createdById,
    });
  }
}
