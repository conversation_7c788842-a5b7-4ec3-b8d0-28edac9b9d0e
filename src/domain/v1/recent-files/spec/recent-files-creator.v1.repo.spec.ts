import { mock } from 'jest-mock-extended';
import { DataSource, SelectQueryBuilder } from 'typeorm';

import { Documents } from '@core/db/entities/documents';
import tzDayjs from '@core/shared/common/common.dayjs';
import { createRepoTestingModule } from '@core/test/test-util/test-util.common';

import {
  ApproveStatus,
  DocumentActionType,
  DocumentType,
} from '../../documents/documents.v1.constant';
import { RecentFilesV1Repo } from '../recent-files.v1.repo';

describe('RecentFilesV1Repo - Creator Methods', () => {
  let repo: RecentFilesV1Repo;
  let dataSource: DataSource;
  let mockQueryBuilder: jest.Mocked<SelectQueryBuilder<Documents>>;

  beforeAll(async () => {
    if (globalThis.dataSource) {
      const module = await createRepoTestingModule(RecentFilesV1Repo);
      repo = module.get(RecentFilesV1Repo);
      dataSource = globalThis.dataSource;
    } else {
      dataSource = mock<DataSource>();
      repo = new RecentFilesV1Repo(dataSource, {} as any);
    }
  });

  beforeEach(() => {
    mockQueryBuilder = {
      createQueryBuilder: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
      getCount: jest.fn(),
    } as any;

    if (!globalThis.dataSource) {
      const mockRepository = {
        createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
        countBy: jest.fn(),
      };
      jest.spyOn(repo, 'from').mockReturnValue(mockRepository as any);
    }
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getRecentTemplatesByCreator', () => {
    const mockCurrentTime = tzDayjs('2024-01-15T10:00:00Z');
    const createdById = 1;
    const paginationOptions = { page: 1, perPage: 10 };
    const basicQuery = {
      orderBy: 'DESC' as const,
      sortBy: 'latestEditedAt',
    };

    const mockTemplateData = {
      id: 1,
      name: 'Test Template',
      status: ApproveStatus.Approved,
      actionType: DocumentActionType.Create,
      backgroundColor: '#FFFFFF',
      reviewedAt: mockCurrentTime.subtract(1, 'day').toDate(),
      createdAt: mockCurrentTime.subtract(2, 'days').toDate(),
      latestEditedAt: mockCurrentTime.subtract(1, 'hour').toDate(),
      thumbnail: { resolutionLowPath: 'template-thumb.jpg' },
      createdBy: {
        id: 1,
        department: { id: 1, nameTh: 'กรมป้องกันและบรรเทาสาธารณภัย' },
      },
    } as any;

    it('should filter by creator ID correctly', async () => {
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([mockTemplateData]);
        mockQueryBuilder.getCount.mockResolvedValue(1);
      }

      await repo.getRecentTemplatesByCreator(createdById, paginationOptions, basicQuery);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.where).toHaveBeenCalledWith(
          'doc.documentType = :documentType',
          { documentType: DocumentType.Template }
        );
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
          'doc.createdById = :createdById',
          { createdById }
        );
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('doc.deletedAt IS NULL');
      }
    });

    it('should apply status filter when provided', async () => {
      const queryWithStatus = {
        ...basicQuery,
        status: ApproveStatus.Draft,
      };

      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      await repo.getRecentTemplatesByCreator(createdById, paginationOptions, queryWithStatus);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
          'doc.status = :status',
          { status: ApproveStatus.Draft }
        );
      }
    });

    it('should return correct data structure', async () => {
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([mockTemplateData]);
        mockQueryBuilder.getCount.mockResolvedValue(1);
      }

      const result = await repo.getRecentTemplatesByCreator(createdById, paginationOptions, basicQuery);

      expect(result).toHaveProperty('datas');
      expect(result).toHaveProperty('pagination');
      expect(Array.isArray(result.datas)).toBe(true);

      if (!globalThis.dataSource) {
        expect(result.datas).toHaveLength(1);
        expect(result.datas[0]).toEqual({
          id: 1,
          name: 'Test Template',
          status: ApproveStatus.Approved,
          actionType: DocumentActionType.Create,
          backgroundColor: '#FFFFFF',
          thumbnail: { resolutionLowPath: 'template-thumb.jpg' },
          createdBy: { id: 1 },
          department: { id: 1, nameTh: 'กรมป้องกันและบรรเทาสาธารณภัย' },
        });
      }
    });
  });

  describe('getRecentReportsByCreator', () => {
    const createdById = 2;
    const paginationOptions = { page: 1, perPage: 10 };
    const basicQuery = {
      orderBy: 'DESC' as const,
      sortBy: 'latestEditedAt',
    };

    it('should filter by creator ID and document type correctly', async () => {
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      await repo.getRecentReportsByCreator(createdById, paginationOptions, basicQuery);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.where).toHaveBeenCalledWith(
          'doc.documentType = :documentType',
          { documentType: DocumentType.Report }
        );
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
          'doc.createdById = :createdById',
          { createdById }
        );
      }
    });
  });

  describe('count methods by creator', () => {
    const createdById = 1;

    it('should have count methods available', () => {
      expect(typeof repo.getApprovedTemplateCountsByCreator).toBe('function');
      expect(typeof repo.getPendingTemplateCountsByCreator).toBe('function');
      expect(typeof repo.getTotalTemplateCountsByCreator).toBe('function');
      expect(typeof repo.getApprovedReportCountsByCreator).toBe('function');
      expect(typeof repo.getPendingReportCountsByCreator).toBe('function');
      expect(typeof repo.getTotalReportCountsByCreator).toBe('function');
    });
  });
});
