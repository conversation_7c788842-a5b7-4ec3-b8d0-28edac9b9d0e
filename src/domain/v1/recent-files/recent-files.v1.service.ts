import { Injectable } from '@nestjs/common';

import { Users } from '@core/db/entities/users';
import { MinioService } from '@core/global/minio/minio.service';
import { PermissionValidatorService } from '@core/global/permission-validator/permission-validator.service';
import { Ok, Res } from '@core/shared/common/common.neverthrow';
import { TypeOrmOrderBy } from '@core/shared/common/common.typeorm';

import { DocumentType } from '../documents/documents.v1.constant';
import { RecentFilesV1Repo } from './recent-files.v1.repo';
import { GetRecentFiles, GetRecentTemplatesByCreator, GetRecentReportsByCreator } from './recent-files.v1.type';
import { GetRecentTemplatesV1HttpParam } from './handler/dto/get-recent-templates.v1.http.dto';
import { GetRecentReportsV1HttpParam } from './handler/dto/get-recent-reports.v1.http.dto';

@Injectable()
export class RecentFilesV1Service {
  constructor(
    private recentFilesRepo: RecentFilesV1Repo,
    private minioService: MinioService,
    private permissionValidator: PermissionValidatorService,
  ) {}

  async getRecentFiles(
    user: Users,
    limit: number = 10,
  ): Promise<Res<GetRecentFiles, ''>> {
    const { datas } = await this.recentFilesRepo.getRecentFiles(limit);

    const transformedData = await Promise.all(
      datas.map(async (data) => {
        const { thumbnail, createdBy, ...other } = data;

        const thumbnailUrl = await this.minioService
          .getPresignedUrl({
            object: thumbnail?.resolutionLowPath || '',
          })
          .catch(() => '');

        const permission = {
          canDelete:
            other.documentType === DocumentType.Template
              ? this.permissionValidator.canDeleteTemplate({
                  user,
                  createdBy,
                  documentStatus: other.status,
                  department: other.department,
                })
              : this.permissionValidator.canDeleteReport({
                  user,
                  createdBy,
                  documentStatus: other.status,
                  department: other.department,
                }),
        };

        return {
          ...other,
          thumbnailUrl,
          permission,
        };
      }),
    );

    return Ok({
      datas: transformedData,
    });
  }

  async getRecentTemplatesByCreator(
    user: Users,
    options: GetRecentTemplatesV1HttpParam,
  ): Promise<Res<GetRecentTemplatesByCreator, ''>> {
    const query = {
      search: options.search,
      startDate: options.startDate,
      endDate: options.endDate,
      departmentIds: options.departmentIds,
      layoutDirections: options.layoutDirections,
      disasterIds: options.disasterIds,
      status: options.status,
      orderBy: options.orderBy || TypeOrmOrderBy.DESC,
      sortBy: options.sortBy || 'latestEditedAt',
    };

    const { datas, pagination } = await this.recentFilesRepo.getRecentTemplatesByCreator(
      user.id,
      {
        page: options.page,
        perPage: options.perPage,
      },
      query,
    );

    const transformedData = await Promise.all(
      datas.map(async (data) => {
        const { thumbnail, createdBy, ...other } = data;

        const thumbnailUrl = await this.minioService
          .getPresignedUrl({
            object: thumbnail?.resolutionLowPath || '',
          })
          .catch(() => '');

        const permission = {
          canDelete: this.permissionValidator.canDeleteTemplate({
            user,
            createdBy,
            documentStatus: other.status,
            department: other.department,
          }),
        };

        return {
          ...other,
          thumbnailUrl,
          permission,
        };
      }),
    );

    const totalApproved = await this.recentFilesRepo.getApprovedTemplateCountsByCreator(user.id);
    const totalPending = await this.recentFilesRepo.getPendingTemplateCountsByCreator(user.id);
    const totalDraft = await this.recentFilesRepo.getDraftTemplateCountsByCreator(user.id);
    const totalTemplates = await this.recentFilesRepo.getTotalTemplateCountsByCreator(user.id);

    return Ok({
      datas: transformedData,
      pagination,
      totalApproved,
      totalPending,
      totalDraft,
      totalTemplates,
    });
  }

  async getRecentReportsByCreator(
    user: Users,
    options: GetRecentReportsV1HttpParam,
  ): Promise<Res<GetRecentReportsByCreator, ''>> {
    const query = {
      search: options.search,
      startDate: options.startDate,
      endDate: options.endDate,
      departmentIds: options.departmentIds,
      layoutDirections: options.layoutDirections,
      disasterIds: options.disasterIds,
      status: options.status,
      orderBy: options.orderBy || TypeOrmOrderBy.DESC,
      sortBy: options.sortBy || 'latestEditedAt',
    };

    const { datas, pagination } = await this.recentFilesRepo.getRecentReportsByCreator(
      user.id,
      {
        page: options.page,
        perPage: options.perPage,
      },
      query,
    );

    const transformedData = await Promise.all(
      datas.map(async (data) => {
        const { thumbnail, createdBy, ...other } = data;

        const thumbnailUrl = await this.minioService
          .getPresignedUrl({
            object: thumbnail?.resolutionLowPath || '',
          })
          .catch(() => '');

        const permission = {
          canDelete: this.permissionValidator.canDeleteReport({
            user,
            createdBy,
            documentStatus: other.status,
            department: other.department,
          }),
        };

        return {
          ...other,
          thumbnailUrl,
          permission,
        };
      }),
    );

    const totalApproved = await this.recentFilesRepo.getApprovedReportCountsByCreator(user.id);
    const totalPending = await this.recentFilesRepo.getPendingReportCountsByCreator(user.id);
    const totalDraft = await this.recentFilesRepo.getDraftReportCountsByCreator(user.id);
    const totalReports = await this.recentFilesRepo.getTotalReportCountsByCreator(user.id);

    return Ok({
      datas: transformedData,
      pagination,
      totalApproved,
      totalPending,
      totalDraft,
      totalReports,
    });
  }
}
